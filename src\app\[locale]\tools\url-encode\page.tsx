"use client";

import { useState } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

import { WechatQRSection } from "@/app/_components/wechat-qr-section";
export default function UrlEncodePage() {
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [mode, setMode] = useState<"encode" | "decode">("encode");
  const [encodeType, setEncodeType] = useState<"component" | "uri">("component");

  const t = useTranslations('pages.tools.tools.urlEncode');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  const processUrl = () => {
    try {
      if (mode === "encode") {
        if (encodeType === "component") {
          setOutput(encodeURIComponent(input));
        } else {
          setOutput(encodeURI(input));
        }
      } else {
        setOutput(decodeURIComponent(input));
      }
    } catch {
      alert(t('errors.processFailed'));
    }
  };

  const clearAll = () => {
    setInput("");
    setOutput("");
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(output);
      alert(t('messages.copied'));
    } catch (err) {
      console.error("Copy failed:", err);
    }
  };

  const swapInputOutput = () => {
    setInput(output);
    setOutput(input);
    setMode(mode === "encode" ? "decode" : "encode");
  };

  return (
    <>
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": t('meta.title'),
            "description": t('meta.description'),
            "url": `${SITE_URL}${getLocalizedHref('/tools/url-encode')}`,
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "permissions": "browser",
            "isAccessibleForFree": true,
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": locale === 'zh' ? "CNY" : "USD"
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container-custom py-20">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
                {t('breadcrumb.home')}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        {/* Controls */}
        <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-8">
          <div className="flex flex-wrap items-center gap-6 mb-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('controls.mode')}
              </label>
              <div className="flex bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
                <button
                  onClick={() => setMode("encode")}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    mode === "encode"
                      ? "bg-orange-600 text-white"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  {t('controls.encode')}
                </button>
                <button
                  onClick={() => setMode("decode")}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    mode === "decode"
                      ? "bg-orange-600 text-white"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  {t('controls.decode')}
                </button>
              </div>
            </div>

            {mode === "encode" && (
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t('controls.encodeType')}
                </label>
                <select
                  value={encodeType}
                  onChange={(e) => setEncodeType(e.target.value as "component" | "uri")}
                  className="px-3 py-1 border border-gray-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
                >
                  <option value="component">encodeURIComponent</option>
                  <option value="uri">encodeURI</option>
                </select>
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-3">
            <button
              onClick={processUrl}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              {mode === "encode" ? t('controls.encode') : t('controls.decode')}
            </button>
            <button
              onClick={swapInputOutput}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              {t('controls.swap')}
            </button>
            <button
              onClick={clearAll}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              {t('controls.clear')}
            </button>
          </div>
        </div>

        {/* Input/Output Areas */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input */}
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {mode === "encode" ? t('sections.originalUrl') : t('sections.encodedUrl')}
              </h3>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {input.length} {t('sections.characters')}
              </span>
            </div>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={
                mode === "encode"
                  ? t('placeholders.encodeInput')
                  : t('placeholders.decodeInput')
              }
              className="w-full h-96 p-4 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white font-mono text-sm resize-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          {/* Output */}
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {mode === "encode" ? t('sections.encodeResult') : t('sections.decodeResult')}
              </h3>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {output.length} {t('sections.characters')}
                </span>
                {output && (
                  <button
                    onClick={copyToClipboard}
                    className="px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700 transition-colors"
                  >
                    {t('sections.copy')}
                  </button>
                )}
              </div>
            </div>
            <textarea
              value={output}
              readOnly
              placeholder={
                mode === "encode"
                  ? t('placeholders.encodeOutput')
                  : t('placeholders.decodeOutput')
              }
              className="w-full h-96 p-4 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white font-mono text-sm resize-none"
            />
          </div>
        </div>

        {/* Usage Tips */}
        <div className="mt-12 bg-orange-50 dark:bg-slate-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('tips.title')}
          </h3>
          <div className="space-y-4 text-gray-600 dark:text-gray-300">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('tips.encodeTypes.title')}</h4>
              <ul className="space-y-1 ml-4">
                {[0, 1].map((index) => {
                  const item = t.raw(`tips.encodeTypes.items.${index}`);
                  return item ? (
                    <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                  ) : null;
                }).filter(Boolean)}
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('tips.commonUses.title')}</h4>
              <ul className="space-y-1 ml-4">
                {[0, 1, 2].map((index) => {
                  const item = t.raw(`tips.commonUses.items.${index}`);
                  return item ? (
                    <li key={index}>• {item}</li>
                  ) : null;
                }).filter(Boolean)}
              </ul>
            </div>
          </div>
        </div>
      </div>
    

      {/* 微信公众号二维码 */}

      <div className="mt-12">

        <WechatQRSection size="medium" />

      </div>

      </div>
    </>
  );
}