"use client";

import Image from "next/image";
import { useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { trackEvent } from "@/lib/gtag";

type Props = {
  className?: string;
  size?: 'small' | 'medium' | 'large';
  showTitle?: boolean;
  showDescription?: boolean;
};

export function WechatQRSection({ 
  className = "", 
  size = 'medium',
  showTitle = true,
  showDescription = true 
}: Props) {
  const locale = useLocale();

  // 根据尺寸设置不同的样式
  const sizeConfig = {
    small: {
      qrSize: 80,
      containerPadding: 'p-3',
      titleSize: 'text-sm',
      descriptionSize: 'text-xs',
      spacing: 'space-y-2'
    },
    medium: {
      qrSize: 120,
      containerPadding: 'p-4',
      titleSize: 'text-base',
      descriptionSize: 'text-sm',
      spacing: 'space-y-3'
    },
    large: {
      qrSize: 160,
      containerPadding: 'p-6',
      titleSize: 'text-lg',
      descriptionSize: 'text-base',
      spacing: 'space-y-4'
    }
  };

  const config = sizeConfig[size];

  const handleQRClick = () => {
    trackEvent.wechatClick();
  };

  return (
    <div className={`bg-gradient-to-br from-green-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl border border-green-100 dark:border-slate-600 ${config.containerPadding} ${className} max-w-md mx-auto`}>
      <div className={`flex flex-col items-center text-center ${config.spacing}`}>
        {showTitle && (
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-4.52-4.635-7.636-10.364-7.636zm-3.785 5.25a.981.981 0 1 1 0 1.962.981.981 0 0 1 0-1.962zm7.568 0a.981.981 0 1 1 0 1.962.981.981 0 0 1 0-1.962z"/>
              <path d="M15.308 9.53c-1.315 0-2.417.363-3.291.998-.874.635-1.411 1.538-1.411 2.567 0 1.315.816 2.417 2.417 3.291l1.538 1.315c.181.181.454.181.635 0l1.538-1.315c1.601-.874 2.417-1.976 2.417-3.291 0-1.029-.537-1.932-1.411-2.567-.874-.635-1.976-.998-3.291-.998z"/>
            </svg>
            <h3 className={`font-semibold text-slate-800 dark:text-white ${config.titleSize}`}>
              {locale === defaultLocale ? '关注微信公众号' : 'Follow WeChat Official Account'}
            </h3>
          </div>
        )}

        {/* 二维码 */}
        <div 
          className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
          onClick={handleQRClick}
        >
          <div className="p-3">
            <Image
              src="/images/qrcode.jpg"
              alt={locale === defaultLocale ? "微信公众号二维码" : "WeChat Official Account QR Code"}
              width={config.qrSize}
              height={config.qrSize}
              className="rounded-lg"
              priority={false}
            />
          </div>
        </div>

        {showDescription && (
          <div className={`text-slate-600 dark:text-slate-300 ${config.descriptionSize} max-w-xs leading-relaxed`}>
            {locale === defaultLocale ? (
              <>
                <p className="font-medium text-slate-700 dark:text-slate-200">扫码关注获取：</p>
                <ul className="mt-1 space-y-1 text-left">
                  <li>• 最新技术文章推送</li>
                  <li>• 独家开发经验分享</li>
                  <li>• 实用工具和资源</li>
                </ul>
              </>
            ) : (
              <>
                <p className="font-medium text-slate-700 dark:text-slate-200">Scan to get:</p>
                <ul className="mt-1 space-y-1 text-left">
                  <li>• Latest tech articles</li>
                  <li>• Exclusive dev insights</li>
                  <li>• Useful tools & resources</li>
                </ul>
              </>
            )}
          </div>
        )}

        {/* 装饰性元素 */}
        <div className="flex items-center gap-1 opacity-60">
          <div className="w-1 h-1 bg-green-400 rounded-full"></div>
          <div className="w-2 h-1 bg-green-300 rounded-full"></div>
          <div className="w-1 h-1 bg-green-400 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
