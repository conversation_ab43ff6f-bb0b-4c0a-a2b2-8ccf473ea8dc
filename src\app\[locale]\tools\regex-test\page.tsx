"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

import { WechatQRSection } from "@/app/_components/wechat-qr-section";
export default function RegexTestPage() {
  const [pattern, setPattern] = useState("");
  const [flags, setFlags] = useState("g");
  const [testString, setTestString] = useState("");
  const [matches, setMatches] = useState<RegExpMatchArray[]>([]);
  const [error, setError] = useState("");
  const [isValid, setIsValid] = useState(true);

  const t = useTranslations('pages.tools.tools.regexTest');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  const commonPatterns = [
    { nameKey: "email", pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$" },
    { nameKey: "phone", pattern: "^1[3-9]\\d{9}$" },
    { nameKey: "idCard", pattern: "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$" },
    { nameKey: "ip", pattern: "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$" },
    { nameKey: "url", pattern: "^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$" },
    { nameKey: "chinese", pattern: "[\\u4e00-\\u9fa5]+" },
    { nameKey: "number", pattern: "\\d+" },
    { nameKey: "letter", pattern: "[a-zA-Z]+" },
  ];

  useEffect(() => {
    const testRegexInner = () => {
      setError("");
      setIsValid(true);
      setMatches([]);

      if (!pattern) {
        return;
      }

      try {
        const regex = new RegExp(pattern, flags);
        
        if (flags.includes('g')) {
          const allMatches = [];
          let match;
          while ((match = regex.exec(testString)) !== null) {
            allMatches.push(match);
            if (match.index === regex.lastIndex) {
              break;
            }
          }
          setMatches(allMatches);
        } else {
          const match = regex.exec(testString);
          setMatches(match ? [match] : []);
        }
      } catch {
        setIsValid(false);
        setError(t('errors.syntaxError'));
        setMatches([]);
      }
    };
    
    testRegexInner();
  }, [pattern, flags, testString]);





  const toggleFlag = (flag: string) => {
    if (flags.includes(flag)) {
      setFlags(flags.replace(flag, ""));
    } else {
      setFlags(flags + flag);
    }
  };

  const highlightMatches = (text: string) => {
    if (!pattern || !isValid || matches.length === 0) {
      return text;
    }

    let result = text;
    let offset = 0;

    matches.forEach((match) => {
      if (match.index !== undefined) {
        const start = match.index + offset;
        const end = start + match[0].length;
        const highlighted = `<mark class="bg-yellow-200 dark:bg-yellow-600 px-1 rounded">${match[0]}</mark>`;
        result = result.slice(0, start) + highlighted + result.slice(end);
        offset += highlighted.length - match[0].length;
      }
    });

    return result;
  };

  return (
    <>
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": t('meta.title'),
            "description": t('meta.description'),
            "url": `${SITE_URL}${getLocalizedHref('/tools/regex-test')}`,
            "applicationCategory": "DeveloperApplication",
            "operatingSystem": "Any",
            "permissions": "browser",
            "isAccessibleForFree": true,
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": locale === 'zh' ? "CNY" : "USD"
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container-custom py-20">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
                {t('breadcrumb.home')}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        {/* Pattern Input */}
        <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('sections.pattern')}
          </h3>
          <div className="flex items-center gap-2 mb-4">
            <span className="text-gray-500 dark:text-gray-400 font-mono text-lg">/</span>
            <input
              type="text"
              value={pattern}
              onChange={(e) => setPattern(e.target.value)}
              placeholder={t('placeholders.pattern')}
              className={`flex-1 p-3 border rounded-lg font-mono text-lg focus:ring-2 focus:border-transparent ${
                isValid
                  ? "border-gray-300 dark:border-slate-600 focus:ring-emerald-500"
                  : "border-red-300 dark:border-red-600 focus:ring-red-500"
              } bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white`}
            />
            <span className="text-gray-500 dark:text-gray-400 font-mono text-lg">/</span>
            <input
              type="text"
              value={flags}
              onChange={(e) => setFlags(e.target.value)}
              placeholder={t('placeholders.flags')}
              className="w-20 p-3 border border-gray-300 dark:border-slate-600 rounded-lg font-mono text-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>

          {/* Flags */}
          <div className="flex flex-wrap gap-2 mb-4">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('flags.label')}</span>
            {[
              { flag: "g", descKey: "g" },
              { flag: "i", descKey: "i" },
              { flag: "m", descKey: "m" },
              { flag: "s", descKey: "s" },
              { flag: "u", descKey: "u" },
              { flag: "y", descKey: "y" },
            ].map(({ flag, descKey }) => (
              <button
                key={flag}
                onClick={() => toggleFlag(flag)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  flags.includes(flag)
                    ? "bg-emerald-600 text-white"
                    : "bg-gray-100 dark:bg-slate-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-600"
                }`}
                title={t(`flags.${descKey}`)}
              >
                {flag}
              </button>
            ))}
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <p className="text-red-700 dark:text-red-400 text-sm">❌ {error}</p>
            </div>
          )}
        </div>

        {/* Common Patterns */}
        <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('sections.commonPatterns')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {commonPatterns.map((item, index) => (
              <button
                key={index}
                onClick={() => setPattern(item.pattern)}
                className="p-3 text-left bg-gray-50 dark:bg-slate-700 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
              >
                <div className="font-medium text-gray-900 dark:text-white text-sm">
                  {t(`commonPatterns.${item.nameKey}`)}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 font-mono mt-1 truncate">
                  {item.pattern}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Test String */}
        <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('sections.testString')}
          </h3>
          <textarea
            value={testString}
            onChange={(e) => setTestString(e.target.value)}
            placeholder={t('placeholders.testString')}
            className="w-full h-40 p-4 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          />
        </div>

        {/* Results */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Highlighted Text */}
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('sections.highlighted')}
            </h3>
            <div
              className="p-4 border border-gray-300 dark:border-slate-600 rounded-lg bg-gray-50 dark:bg-slate-700 min-h-40 whitespace-pre-wrap font-mono text-sm text-gray-900 dark:text-white"
              dangerouslySetInnerHTML={{
                __html: highlightMatches(testString) || t('placeholders.highlightedEmpty')
              }}
            />
          </div>

          {/* Match Details */}
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('sections.matchDetails')} ({matches.length} {t('matchDetails.matchCount')})
            </h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {matches.length > 0 ? (
                matches.map((match, index) => (
                  <div
                    key={index}
                    className="p-3 bg-gray-50 dark:bg-slate-700 rounded-lg"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {t('matchDetails.match')} {index + 1}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {t('matchDetails.position')} {match.index}-{(match.index || 0) + match[0].length - 1}
                      </span>
                    </div>
                    <div className="font-mono text-sm text-gray-900 dark:text-white bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded">
                      {match[0]}
                    </div>
                    {match.length > 1 && (
                      <div className="mt-2">
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                          {t('matchDetails.captureGroups')}
                        </div>
                        {match.slice(1).map((group, groupIndex) => (
                          <div
                            key={groupIndex}
                            className="text-xs font-mono text-gray-700 dark:text-gray-300"
                          >
                            ${groupIndex + 1}: {group || t('matchDetails.empty')}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                  {pattern && testString ? t('placeholders.noMatches') : t('placeholders.startTesting')}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Usage Tips */}
        <div className="mt-12 bg-emerald-50 dark:bg-slate-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('tips.title')}
          </h3>
          <div className="space-y-4 text-gray-600 dark:text-gray-300">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('tips.metacharacters.title')}</h4>
              <ul className="space-y-1 ml-4 text-sm">
                {[0, 1, 2, 3, 4, 5].map((index) => {
                  const item = t.raw(`tips.metacharacters.items.${index}`);
                  return item ? (
                    <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                  ) : null;
                }).filter(Boolean)}
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('tips.characterClasses.title')}</h4>
              <ul className="space-y-1 ml-4 text-sm">
                {[0, 1, 2, 3, 4].map((index) => {
                  const item = t.raw(`tips.characterClasses.items.${index}`);
                  return item ? (
                    <li key={index} dangerouslySetInnerHTML={{ __html: `• ${item}` }} />
                  ) : null;
                }).filter(Boolean)}
              </ul>
            </div>
          </div>
        </div>
      </div>
    

      {/* 微信公众号二维码 */}

      <div className="mt-12">

        <WechatQRSection size="medium" />

      </div>

      </div>
    </>
  );
}