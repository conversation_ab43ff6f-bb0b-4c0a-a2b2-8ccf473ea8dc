---
featured: false
featuredOrder: 1
featuredReason: "从用户角度分析Cursor面临的挑战和机遇。"
title: "Cursor会被巨头干掉吗？一个开发者的观察"
excerpt: "最近一直在用Cursor写代码，感觉确实不错。但看到各大厂商都在发力AI编程工具，不禁担心这个小而美的产品能撑多久。"
coverImage: "/assets/blog/20.jpeg"
date: "2025-07-07"
lastModified: "2025-07-07"
author:
  name: 老夫撸代码
  picture: "/assets/blog/authors/tim.jpeg"
---

## 为什么我开始用 Cursor

说实话，刚开始听说 Cursor 的时候，我是拒绝的。又一个 AI 编程工具？GitHub Copilot 不是已经够用了吗？

但架不住朋友的推荐，试用了一下，发现确实有点意思。[Cursor](https://cursor.com/en)最吸引我的地方是，它不像其他工具那样只是在现有编辑器里加个 AI 插件，而是从一开始就围绕 AI 来设计整个编码体验。

最让我惊喜的是，从 VS Code 切换过来几乎没有学习成本。毕竟 Cursor 本身就是基于 VS Code 开发的，我的快捷键、插件、主题都能直接用。但同时又能享受到更智能的代码生成、自然语言编辑，还能直接问它关于整个项目的问题。

用了几个月下来，确实提高了不少效率。特别是写一些重复性代码的时候，基本上描述一下需求，它就能生成八九不离十的代码。

## 但是，我开始担心了

不过最近用着用着，我开始有点担心。不是担心 Cursor 不好用，而是担心它能撑多久。

### 成本问题：每次 AI 调用都在烧钱

首先是成本问题。Cursor 官网上很坦诚地说了，大语言模型运行成本很高。我每次让它生成代码、问问题，背后都是在调用 OpenAI 或者 Claude 的 API，这些都是要花钱的。

我现在用的是 Pro 版本，每个月 20 美元。说实话，对于提升的效率来说，这个价格还算合理。但问题是，如果用户量上去了，Cursor 的成本会不会也跟着飙升？到时候会不会涨价？

而且 GitHub Copilot 现在也就 10 美元一个月，还有微软和 GitHub 的生态支持。从纯粹的性价比角度，Cursor 确实有点尴尬。

### 竞争激烈：大厂都在发力

更让我担心的是竞争环境。现在各大厂商都在 AI 编程工具上发力：

**Anthropic**：Claude 本来就是 Cursor 用的模型之一，现在 Anthropic 自己也推出了终端 AI 工具，直接跟 Cursor 抢用户。

**Google**：Gemini CLI 工具免费开放，每天 1000 次请求，对于很多开发者来说已经够用了。而且 Google 的代码理解能力确实不错。

**微软**：GitHub Copilot 背靠微软和 GitHub 的生态，用户基数庞大，而且还在不断升级功能。

最关键的是，Cursor 基于 VS Code 开发，这意味着微软随时可以把 Cursor 的功能集成到 VS Code 里。到时候，用户还有什么理由单独付费用 Cursor 呢？

这让我想起了当年很多基于 Chrome 开发的浏览器，最后都被 Chrome 本身的功能更新给"干掉"了。

### 隐私问题：代码会被收集吗？

还有一个让我有点纠结的问题是隐私。Cursor 的隐私政策里提到，除非开启"隐私模式"，否则可能会收集我们的代码片段来改进产品。

虽然这对 AI 公司来说很正常，但作为开发者，我还是有点担心。特别是在处理一些敏感项目的时候，真的不希望代码被上传到云端。

好在 Cursor 提供了隐私模式，但开启后功能会受到一些限制。这就是一个权衡问题：要便利还是要隐私？

## Cursor 在做什么努力？

不过话说回来，Cursor 团队也不是坐以待毙。我观察到他们在几个方向上在努力：

**开发自己的模型**：最近 Cursor 推出了一些独有的功能，比如"Tab-to-diff"，这些是通过简单调用第三方 API 做不到的。如果能在某些特定场景下做出差异化，还是有机会的。

**扩展到更广的平台**：他们推出了 Web 版本，让用户可以在浏览器里管理 AI 编码助手。这个思路不错，不只是做编辑器，而是做整个开发工作流的 AI 平台。

**社区建设**：Cursor 在 GitHub 上很活跃，经常和用户互动，收集反馈。虽然用户基数比不上大厂，但用户粘性还是挺高的。

## 我的看法

作为一个普通开发者，我对 Cursor 的未来既担心又期待。

担心的是，在这个竞争激烈的市场里，小公司确实很难生存。特别是面对微软、Google 这样的巨头，资源和生态都不在一个量级上。

但我也期待 Cursor 能够坚持下去，因为它确实给我带来了不一样的编程体验。有时候，小而美的产品反而能做出大公司做不出来的创新。

不管怎样，这场 AI 编程工具的竞争对我们开发者来说是好事。工具越来越智能，我们的工作效率也会越来越高。

至于 Cursor 能不能在巨头的围攻下生存下来，就看它能不能找到自己独特的价值定位了。希望它能成功，也希望这个市场能有更多优秀的工具出现。

毕竟，最终受益的还是我们这些写代码的人。
