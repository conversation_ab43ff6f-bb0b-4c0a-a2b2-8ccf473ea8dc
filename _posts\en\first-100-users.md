---
excerpt: "Built a product but no one's using it? Sharing my real journey from 0 to 100 users, including the mistakes I made and methods that actually worked. Not theory, all hands-on experience."
coverImage: "/assets/blog/5.png"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
featured: false
featuredOrder: 4
featuredReason: "Real product cold start experience sharing"
title: "Built a Product but No One's Using It? How I Found My First 100 Users"
date: "2025-06-01"
lastModified: "2025-06-01"
---

Last year I built a small tool, spent 3 months developing it, and felt pretty good about it. After launching, I eagerly waited for users to flood in, but... a week passed and only my mom had registered (and I had to beg her to do it).

That feeling of defeat was really hard to take. The product was clearly useful, so why didn't anyone know about it?

Later I spent 2 months trying various methods to find users, stepped on countless landmines, but also found some effective approaches. Eventually I went from 0 to 100 real users.

Today I'm sharing my real experience, hoping to help fellow indie developers who are also struggling with user acquisition.

## Mindset Adjustment: Don't Dream of Overnight Success

At first, I also fantasized that my product would go viral as soon as it launched, with users flocking in. Reality slapped me in the face.

Later I understood that in the early stages, you have to do those "inelegant" things:

**Find users one by one**
I literally joined WeChat groups one by one, sent private messages one by one, and contacted people via email one by one. Although inefficient, every user was precious.

**Treat users as friends**
I would remember every early user's name, reply immediately when they had problems, and even proactively ask about their experience. One user said my customer service was better than their company's.

**Accept rejection**
Rejection is the norm. I was rejected countless times. But as long as one person was willing to try, it was a victory.

## Methods I Tried (Effective and Ineffective)

### Community Promotion: Most Effective Channel

This was where I got the most users, but I also stepped on quite a few landmines.

**V2EX**: My first batch of users mainly came from here. But never post direct ads - you'll get roasted. I replied in relevant discussions, mentioning I made a small tool to solve similar problems, then left a link.

**WeChat Groups**: Joined over a dozen relevant WeChat groups, but most don't allow ads. My approach was to be active in the group for a while, build trust, then privately message group members to introduce the product.

**Zhihu**: Wrote several related answers, mentioning my product at the end. But Zhihu's traffic conversion rate is relatively low - many views, few actual registrations.

**Reddit**: If your product targets overseas users, Reddit is a good place. But you need to find the right Subreddit and truly provide value, not just advertise.

### Content Marketing: Slow but Effective

**Writing blogs**: I wrote a series of articles around the problems my product solves, gradually gaining traffic through SEO. Although slow to show results, the quality was high.

**Making videos**: Posted several tutorial videos on Bilibili and YouTube with decent results. Videos build trust more easily than text.

**Free tools**: I made a simplified free version of the tool and put it on GitHub, attracting quite a few developers' attention.

### Product Hunt: One-time Traffic Burst

I launched my product on Product Hunt, got over 200 visits that day, but only converted 5 users. Lots of traffic, but very low conversion rate.

However, this launch taught me how to write product descriptions and make demo videos - skills that proved useful later.

### Friends and Networks: Most Overlooked Channel

**Friends and colleagues**: I shamelessly asked everyone I knew to try it. Although most people only registered out of courtesy, a few became real users.

**Industry groups**: I shared the product in several industry WeChat groups with better results than expected. The key is choosing the right groups where people actually have this need.

## Mistakes I Made

**Paying for ads**: I spent 500 yuan on Google Ads, got over 100 clicks, but only 2 registrations. For early-stage products, paid advertising ROI is too low.

**Mass emailing**: I bought an email list and sent 1000 emails, got marked as spam, and almost got banned by the email service provider.

**Ranking manipulation**: I once considered paying people to boost Product Hunt rankings, but didn't go through with it. Looking back, even if effective, this wouldn't bring real users.

## User Retention: Getting Them to Actually Use It

Getting users is just the first step; keeping them is key.

**Simplify registration**: I reduced registration steps from 5 to 2, improving conversion rate by 30%.

**Onboarding guide**: I created a simple product tour to help new users quickly understand core features.

**Timely responses**: I guaranteed replies to all user questions and feedback within 2 hours. This commitment impressed many users.

**Build community**: I created a user WeChat group where people share usage tips and help each other. This group has now become an important source of product improvement feedback.

## Data Review

Final user source distribution:

- V2EX and tech communities: 40%
- Friend referrals: 25%
- Content marketing (blogs, videos): 20%
- Product Hunt: 10%
- Other channels: 5%

This data shows that community promotion and word-of-mouth are the most effective approaches.

## Some Advice

**Focus on one channel**: Don't try everything. Choose the channel most suitable for your product and dig deep.

**Value user feedback**: Every early user's feedback is precious. Take every suggestion seriously.

**Stay patient**: From 0 to 100 users took me 2 months. The process was agonizing, but persistence pays off.

**Record data**: Track each channel's effectiveness so you know which methods work and which waste time.

## Final Thoughts

Looking back, that period of finding users was tough but meaningful. It not only helped me find users but, more importantly, helped me truly understand user needs.

If you're also struggling with users, don't get discouraged. Every successful product goes through this stage. The key is to get out there and actively seek users, not wait for them to come to you.

Finally, if this article helps you, please share it with more friends who need it. We indie developers should help each other and grow together.
