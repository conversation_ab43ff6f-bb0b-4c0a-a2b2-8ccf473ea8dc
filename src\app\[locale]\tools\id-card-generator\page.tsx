"use client";

import { useState } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from 'next-intl';
import { defaultLocale } from '@/i18n';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import { WechatQRSection } from "@/app/_components/wechat-qr-section";
import { Shuffle, Copy, RefreshCw, User, Calendar, MapPin, Settings, AlertTriangle } from "lucide-react";

// 生成的身份证信息接口
interface GeneratedIdCard {
  idNumber: string;
  name: string;
  province: string;
  city: string;
  county: string;
  birthDate: string;
  age: number;
  gender: string;
  constellation: string;
  zodiac: string;
}

// 地区代码映射（简化版，包含主要城市）
const REGION_CODES: { [key: string]: { province: string; city?: string; county?: string } } = {
  "110101": { province: "北京市", city: "北京市", county: "东城区" },
  "110102": { province: "北京市", city: "北京市", county: "西城区" },
  "110105": { province: "北京市", city: "北京市", county: "朝阳区" },
  "110106": { province: "北京市", city: "北京市", county: "丰台区" },
  "110108": { province: "北京市", city: "北京市", county: "海淀区" },
  "120101": { province: "天津市", city: "天津市", county: "和平区" },
  "120102": { province: "天津市", city: "天津市", county: "河东区" },
  "120103": { province: "天津市", city: "天津市", county: "河西区" },
  "130100": { province: "河北省", city: "石家庄市" },
  "130200": { province: "河北省", city: "唐山市" },
  "130300": { province: "河北省", city: "秦皇岛市" },
  "140100": { province: "山西省", city: "太原市" },
  "140200": { province: "山西省", city: "大同市" },
  "150100": { province: "内蒙古自治区", city: "呼和浩特市" },
  "150200": { province: "内蒙古自治区", city: "包头市" },
  "210100": { province: "辽宁省", city: "沈阳市" },
  "210200": { province: "辽宁省", city: "大连市" },
  "220100": { province: "吉林省", city: "长春市" },
  "220200": { province: "吉林省", city: "吉林市" },
  "230100": { province: "黑龙江省", city: "哈尔滨市" },
  "230200": { province: "黑龙江省", city: "齐齐哈尔市" },
  "310101": { province: "上海市", city: "上海市", county: "黄浦区" },
  "310104": { province: "上海市", city: "上海市", county: "徐汇区" },
  "310105": { province: "上海市", city: "上海市", county: "长宁区" },
  "310115": { province: "上海市", city: "上海市", county: "浦东新区" },
  "320100": { province: "江苏省", city: "南京市" },
  "320200": { province: "江苏省", city: "无锡市" },
  "320300": { province: "江苏省", city: "徐州市" },
  "320500": { province: "江苏省", city: "苏州市" },
  "330100": { province: "浙江省", city: "杭州市" },
  "330200": { province: "浙江省", city: "宁波市" },
  "330300": { province: "浙江省", city: "温州市" },
  "340100": { province: "安徽省", city: "合肥市" },
  "340200": { province: "安徽省", city: "芜湖市" },
  "350100": { province: "福建省", city: "福州市" },
  "350200": { province: "福建省", city: "厦门市" },
  "360100": { province: "江西省", city: "南昌市" },
  "370100": { province: "山东省", city: "济南市" },
  "370200": { province: "山东省", city: "青岛市" },
  "370300": { province: "山东省", city: "淄博市" },
  "410100": { province: "河南省", city: "郑州市" },
  "410200": { province: "河南省", city: "开封市" },
  "410300": { province: "河南省", city: "洛阳市" },
  "420100": { province: "湖北省", city: "武汉市" },
  "420200": { province: "湖北省", city: "黄石市" },
  "430100": { province: "湖南省", city: "长沙市" },
  "430200": { province: "湖南省", city: "株洲市" },
  "440100": { province: "广东省", city: "广州市" },
  "440300": { province: "广东省", city: "深圳市" },
  "440400": { province: "广东省", city: "珠海市" },
  "440600": { province: "广东省", city: "佛山市" },
  "450100": { province: "广西壮族自治区", city: "南宁市" },
  "450200": { province: "广西壮族自治区", city: "柳州市" },
  "460100": { province: "海南省", city: "海口市" },
  "460200": { province: "海南省", city: "三亚市" },
  "500101": { province: "重庆市", city: "重庆市", county: "万州区" },
  "500103": { province: "重庆市", city: "重庆市", county: "渝中区" },
  "500105": { province: "重庆市", city: "重庆市", county: "江北区" },
  "510100": { province: "四川省", city: "成都市" },
  "510300": { province: "四川省", city: "自贡市" },
  "520100": { province: "贵州省", city: "贵阳市" },
  "530100": { province: "云南省", city: "昆明市" },
  "540100": { province: "西藏自治区", city: "拉萨市" },
  "610100": { province: "陕西省", city: "西安市" },
  "620100": { province: "甘肃省", city: "兰州市" },
  "630100": { province: "青海省", city: "西宁市" },
  "640100": { province: "宁夏回族自治区", city: "银川市" },
  "650100": { province: "新疆维吾尔自治区", city: "乌鲁木齐市" },
};

// 姓氏数据库
const SURNAMES = [
  '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
  '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
  '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧',
  '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕',
  '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎',
  '余', '潘', '杜', '戴', '夏', '钟', '汪', '田', '任', '姜',
  '范', '方', '石', '姚', '谭', '廖', '邹', '熊', '金', '陆',
  '郝', '孔', '白', '崔', '康', '毛', '邱', '秦', '江', '史',
  '顾', '侯', '邵', '孟', '龙', '万', '段', '漕', '钱', '汤'
];

// 男性名字数据库
const MALE_NAMES = {
  two: [
    '伟', '强', '磊', '军', '勇', '涛', '明', '超', '亮', '刚',
    '平', '辉', '鹏', '华', '飞', '鑫', '波', '斌', '凯', '杰',
    '峰', '龙', '浩', '宇', '健', '博', '威', '海', '阳', '昊',
    '天', '志', '东', '建', '文', '武', '新', '利', '清', '飞'
  ],
  three: [
    '志强', '建华', '志明', '建国', '志华', '建军', '志勇', '建平',
    '志刚', '建伟', '志峰', '建新', '志龙', '建波', '志超', '建辉',
    '志飞', '建斌', '志鹏', '建凯', '志杰', '建峰', '志浩', '建龙',
    '志宇', '建博', '志健', '建威', '志海', '建阳', '志昊', '建天',
    '志东', '建文', '志武', '建利', '志清', '建飞', '志磊', '建超',
    '志涛', '建亮', '志刚', '建平', '志辉', '建鹏', '志华', '建飞'
  ]
};

// 女性名字数据库
const FEMALE_NAMES = {
  two: [
    '丽', '娜', '敏', '静', '秀', '慧', '美', '娟', '英', '华',
    '玲', '芳', '燕', '红', '霞', '艳', '梅', '莉', '兰', '凤',
    '洁', '琳', '素', '云', '莲', '真', '环', '雪', '荣', '爱',
    '妍', '萍', '颖', '瑶', '怡', '婷', '丹', '蓉', '薇', '菁'
  ],
  three: [
    '淑华', '淑英', '淑娟', '淑芳', '淑梅', '淑兰', '淑红', '淑霞',
    '淑玲', '淑燕', '淑丽', '淑敏', '淑静', '淑秀', '淑慧', '淑美',
    '雅琳', '雅芳', '雅丽', '雅静', '雅秀', '雅慧', '雅美', '雅娟',
    '雅英', '雅华', '雅玲', '雅燕', '雅红', '雅霞', '雅艳', '雅梅',
    '晓丽', '晓敏', '晓静', '晓秀', '晓慧', '晓美', '晓娟', '晓英',
    '晓华', '晓玲', '晓芳', '晓燕', '晓红', '晓霞', '晓艳', '晓梅'
  ]
};

export default function IdCardGeneratorPage() {
  const [selectedRegion, setSelectedRegion] = useState<string>("");
  const [birthYear, setBirthYear] = useState<number>(1990);
  const [birthMonth, setBirthMonth] = useState<number>(1);
  const [birthDay, setBirthDay] = useState<number>(1);
  const [gender, setGender] = useState<string>("random"); // male, female, random
  const [generatedCard, setGeneratedCard] = useState<GeneratedIdCard | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const t = useTranslations('pages.tools.tools.idCardGenerator');
  const tSite = useTranslations('site');
  const locale = useLocale();

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 获取翻译后的地区名称
  const getTranslatedRegionName = (chineseName: string): string => {
    // 如果是英文环境，尝试获取翻译，如果没有翻译则返回原始中文名称
    if (locale === 'en') {
      // 创建一个基本的地区名称映射
      const basicRegionMap: { [key: string]: string } = {
        '北京市': 'Beijing',
        '天津市': 'Tianjin',
        '河北省': 'Hebei Province',
        '石家庄市': 'Shijiazhuang',
        '唐山市': 'Tangshan',
        '秦皇岛市': 'Qinhuangdao',
        '山西省': 'Shanxi Province',
        '太原市': 'Taiyuan',
        '大同市': 'Datong',
        '内蒙古自治区': 'Inner Mongolia',
        '呼和浩特市': 'Hohhot',
        '包头市': 'Baotou',
        '辽宁省': 'Liaoning Province',
        '沈阳市': 'Shenyang',
        '大连市': 'Dalian',
        '吉林省': 'Jilin Province',
        '长春市': 'Changchun',
        '吉林市': 'Jilin',
        '黑龙江省': 'Heilongjiang Province',
        '哈尔滨市': 'Harbin',
        '齐齐哈尔市': 'Qiqihar',
        '上海市': 'Shanghai',
        '江苏省': 'Jiangsu Province',
        '南京市': 'Nanjing',
        '无锡市': 'Wuxi',
        '徐州市': 'Xuzhou',
        '苏州市': 'Suzhou',
        '浙江省': 'Zhejiang Province',
        '杭州市': 'Hangzhou',
        '宁波市': 'Ningbo',
        '温州市': 'Wenzhou',
        '安徽省': 'Anhui Province',
        '合肥市': 'Hefei',
        '芜湖市': 'Wuhu',
        '福建省': 'Fujian Province',
        '福州市': 'Fuzhou',
        '厦门市': 'Xiamen',
        '江西省': 'Jiangxi Province',
        '南昌市': 'Nanchang',
        '山东省': 'Shandong Province',
        '济南市': 'Jinan',
        '青岛市': 'Qingdao',
        '淄博市': 'Zibo',
        '河南省': 'Henan Province',
        '郑州市': 'Zhengzhou',
        '开封市': 'Kaifeng',
        '洛阳市': 'Luoyang',
        '湖北省': 'Hubei Province',
        '武汉市': 'Wuhan',
        '黄石市': 'Huangshi',
        '湖南省': 'Hunan Province',
        '长沙市': 'Changsha',
        '株洲市': 'Zhuzhou',
        '广东省': 'Guangdong Province',
        '广州市': 'Guangzhou',
        '深圳市': 'Shenzhen',
        '珠海市': 'Zhuhai',
        '佛山市': 'Foshan',
        '广西壮族自治区': 'Guangxi',
        '南宁市': 'Nanning',
        '柳州市': 'Liuzhou',
        '海南省': 'Hainan Province',
        '海口市': 'Haikou',
        '三亚市': 'Sanya',
        '重庆市': 'Chongqing',
        '万州区': 'Wanzhou District',
        '四川省': 'Sichuan Province',
        '成都市': 'Chengdu',
        '自贡市': 'Zigong',
        '贵州省': 'Guizhou Province',
        '贵阳市': 'Guiyang',
        '云南省': 'Yunnan Province',
        '昆明市': 'Kunming',
        '西藏自治区': 'Tibet',
        '拉萨市': 'Lhasa',
        '陕西省': 'Shaanxi Province',
        '西安市': "Xi'an",
        '甘肃省': 'Gansu Province',
        '兰州市': 'Lanzhou',
        '青海省': 'Qinghai Province',
        '西宁市': 'Xining',
        '宁夏回族自治区': 'Ningxia',
        '银川市': 'Yinchuan',
        '新疆维吾尔自治区': 'Xinjiang',
        '乌鲁木齐市': 'Urumqi',
        // 区县
        '东城区': 'Dongcheng District',
        '西城区': 'Xicheng District',
        '朝阳区': 'Chaoyang District',
        '丰台区': 'Fengtai District',
        '海淀区': 'Haidian District',
        '和平区': 'Heping District',
        '河东区': 'Hedong District',
        '河西区': 'Hexi District',
        '黄浦区': 'Huangpu District',
        '徐汇区': 'Xuhui District',
        '长宁区': 'Changning District',
        '浦东新区': 'Pudong New Area',
        '渝中区': 'Yuzhong District',
        '江北区': 'Jiangbei District'
      };

      return basicRegionMap[chineseName] || chineseName;
    }

    // 中文环境直接返回原始名称
    return chineseName;
  };

  // 获取地区代码数组
  const regionCodes = Object.keys(REGION_CODES);

  // 计算校验位
  const calculateCheckDigit = (id17: string): string => {
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(id17.charAt(i)) * weights[i];
    }
    
    return checkCodes[sum % 11];
  };

  // 获取星座
  const getConstellation = (month: number, day: number): string => {
    const constellations = [
      { key: 'aquarius', start: [1, 20], end: [2, 18] },
      { key: 'pisces', start: [2, 19], end: [3, 20] },
      { key: 'aries', start: [3, 21], end: [4, 19] },
      { key: 'taurus', start: [4, 20], end: [5, 20] },
      { key: 'gemini', start: [5, 21], end: [6, 21] },
      { key: 'cancer', start: [6, 22], end: [7, 22] },
      { key: 'leo', start: [7, 23], end: [8, 22] },
      { key: 'virgo', start: [8, 23], end: [9, 22] },
      { key: 'libra', start: [9, 23], end: [10, 23] },
      { key: 'scorpio', start: [10, 24], end: [11, 22] },
      { key: 'sagittarius', start: [11, 23], end: [12, 21] },
      { key: 'capricorn', start: [12, 22], end: [1, 19] }
    ];

    for (const constellation of constellations) {
      const [startMonth, startDay] = constellation.start;
      const [endMonth, endDay] = constellation.end;

      if (startMonth === endMonth) {
        if (month === startMonth && day >= startDay && day <= endDay) {
          return t(`constellations.${constellation.key}`);
        }
      } else {
        if ((month === startMonth && day >= startDay) ||
            (month === endMonth && day <= endDay)) {
          return t(`constellations.${constellation.key}`);
        }
      }
    }

    return t('messages.unknown');
  };

  // 获取生肖
  const getZodiac = (year: number): string => {
    const zodiacs = ['monkey', 'rooster', 'dog', 'pig', 'rat', 'ox', 'tiger', 'rabbit', 'dragon', 'snake', 'horse', 'goat'];
    return t(`zodiac.${zodiacs[year % 12]}`);
  };

  // 生成随机姓名
  const generateRandomName = (genderType: string): string => {
    const surname = SURNAMES[Math.floor(Math.random() * SURNAMES.length)];
    const nameLength = Math.random() < 0.6 ? 'two' : 'three'; // 60%概率生成2字名，40%概率生成3字名
    
    let givenName: string;
    if (genderType === t('settings.gender.male')) {
      const names = MALE_NAMES[nameLength];
      givenName = names[Math.floor(Math.random() * names.length)];
    } else {
      const names = FEMALE_NAMES[nameLength];
      givenName = names[Math.floor(Math.random() * names.length)];
    }
    
    return surname + givenName;
  };

  // 生成身份证号码
  const generateIdCard = () => {
    setIsGenerating(true);
    
    setTimeout(() => {
      // 选择地区代码
      const regionCode = selectedRegion || regionCodes[Math.floor(Math.random() * regionCodes.length)];
      
      // 生成出生日期
      const year = birthYear;
      const month = birthMonth;
      const day = birthDay;
      
      // 生成顺序码（第15-17位）
      let sequenceCode = Math.floor(Math.random() * 999).toString().padStart(3, '0');
      
      // 根据性别设置顺序码的最后一位（奇数为男，偶数为女）
      if (gender === 'male') {
        // 确保最后一位是奇数
        const lastDigit = parseInt(sequenceCode.charAt(2));
        if (lastDigit % 2 === 0) {
          sequenceCode = sequenceCode.substring(0, 2) + (lastDigit === 9 ? '7' : (lastDigit + 1).toString());
        }
      } else if (gender === 'female') {
        // 确保最后一位是偶数
        const lastDigit = parseInt(sequenceCode.charAt(2));
        if (lastDigit % 2 === 1) {
          sequenceCode = sequenceCode.substring(0, 2) + (lastDigit === 9 ? '8' : (lastDigit + 1).toString());
        }
      }
      
      // 组合前17位
      const birthDateStr = `${year}${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}`;
      const id17 = regionCode + birthDateStr + sequenceCode;
      
      // 计算校验位
      const checkDigit = calculateCheckDigit(id17);
      
      // 完整身份证号码
      const idNumber = id17 + checkDigit;
      
      // 获取地区信息
      const regionInfo = REGION_CODES[regionCode];
      
      // 计算年龄
      const currentDate = new Date();
      const age = currentDate.getFullYear() - year - 
                  (currentDate.getMonth() < month - 1 || 
                   (currentDate.getMonth() === month - 1 && currentDate.getDate() < day) ? 1 : 0);
      
      // 确定性别
      const genderCode = parseInt(sequenceCode.charAt(2));
      const actualGender = genderCode % 2 === 0 ? t('settings.gender.female') : t('settings.gender.male');

      // 格式化出生日期
      const formattedBirthDate = locale === 'zh'
        ? `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`
        : `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`;
      
      // 生成姓名
      const generatedName = generateRandomName(actualGender);
      
      const result: GeneratedIdCard = {
        idNumber,
        name: generatedName,
        province: regionInfo.province,
        city: regionInfo.city || '',
        county: regionInfo.county || '',
        birthDate: formattedBirthDate,
        age,
        gender: actualGender,
        constellation: getConstellation(month, day),
        zodiac: getZodiac(year)
      };
      
      setGeneratedCard(result);
      setIsGenerating(false);
    }, 500);
  };

  // 随机生成
  const randomGenerate = () => {
    // 随机选择地区
    setSelectedRegion(regionCodes[Math.floor(Math.random() * regionCodes.length)]);
    
    // 随机生成出生日期（1950-2005年）
    const randomYear = Math.floor(Math.random() * (2005 - 1950 + 1)) + 1950;
    const randomMonth = Math.floor(Math.random() * 12) + 1;
    const daysInMonth = new Date(randomYear, randomMonth, 0).getDate();
    const randomDay = Math.floor(Math.random() * daysInMonth) + 1;
    
    setBirthYear(randomYear);
    setBirthMonth(randomMonth);
    setBirthDay(randomDay);
    
    // 随机性别
    setGender('random');
    
    // 生成身份证
    setTimeout(() => {
      generateIdCard();
    }, 100);
  };

  // 复制身份证号码
  const copyIdCard = async () => {
    if (!generatedCard) return;
    
    try {
      await navigator.clipboard.writeText(generatedCard.idNumber);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch {
      console.error(t('messages.copyFailed'));
    }
  };

  // 复制完整信息
  const copyFullInfo = async () => {
    if (!generatedCard) return;
    
    const fullInfo = `${t('result.name')}：${generatedCard.name}
${t('result.idNumber')}：${generatedCard.idNumber}
${t('result.region')}：${getTranslatedRegionName(generatedCard.province)}${generatedCard.city ? getTranslatedRegionName(generatedCard.city) : ''}${generatedCard.county ? getTranslatedRegionName(generatedCard.county) : ''}
${t('result.birthDate')}：${generatedCard.birthDate}
${t('result.age')}：${generatedCard.age}${t('result.years')}
${t('result.gender')}：${generatedCard.gender}
${t('result.constellation')}：${generatedCard.constellation}
${t('result.zodiac')}：${generatedCard.zodiac}`;
    
    try {
      await navigator.clipboard.writeText(fullInfo);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error(t('messages.copyFailed'), err);
    }
  };

  // 获取当前年份
  const currentYear = new Date().getFullYear();

  // 如果是英文环境，显示说明页面
  if (locale === 'en') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              🆔 Chinese ID Card Generator
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Generate valid Chinese ID card numbers for testing purposes
            </p>

            <div className="max-w-2xl mx-auto bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-amber-800 dark:text-amber-200 mb-4">
                🇨🇳 Chinese-Specific Tool
              </h2>
              <p className="text-amber-700 dark:text-amber-300 mb-4">
                This tool generates Chinese ID card numbers (身份证号码) following the 18-digit format used in mainland China.
                It includes region codes, birth dates, sequence numbers, and check digits according to Chinese national standards.
              </p>
              <p className="text-amber-700 dark:text-amber-300 mb-4">
                <strong>Features:</strong>
              </p>
              <ul className="text-amber-700 dark:text-amber-300 mb-6 text-left list-disc list-inside space-y-1">
                <li>Generate by specific region (province/city/county)</li>
                <li>Set birth date and gender preferences</li>
                <li>Generate random names with the ID</li>
                <li>Calculate age and zodiac sign</li>
                <li>Validate checksum automatically</li>
              </ul>

              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded p-4 mb-6">
                <p className="text-red-700 dark:text-red-300 text-sm">
                  <strong>⚠️ For Testing Only:</strong> Generated IDs are for development and testing purposes only.
                  Do not use for illegal activities or identity fraud.
                </p>
              </div>

              <div className="flex justify-center">
                <Link
                  href="/tools/id-card-generator"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <User className="w-5 h-5 mr-2" />
                  Use Tool (Chinese Interface)
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": t('title'),
            "description": t('description'),
            "url": `${SITE_URL}/tools/id-card-generator`,
            "applicationCategory": "DeveloperApplication",
            "operatingSystem": "All",
            "permissions": "browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* 主要内容 */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
                {t('breadcrumb.home')}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
              </div>
            </li>
          </ol>
        </nav>
        {/* 工具介绍 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full mb-4">
            <Shuffle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {t('title')}
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            {t('description')}
          </p>
        </div>

        {/* 警告提示 */}
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-amber-800 dark:text-amber-200">
              <p className="font-medium mb-1">{t('warning.title')}</p>
              <p>{t('warning.text')}</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 参数设置 */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-2 mb-6">
              <Settings className="w-5 h-5 text-green-600 dark:text-green-400" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('settings.title')}</h3>
            </div>

            <div className="space-y-6">
              {/* 地区选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <MapPin className="w-4 h-4 inline mr-1" />
                  {t('settings.region.label')}
                </label>
                <select
                  value={selectedRegion}
                  onChange={(e) => setSelectedRegion(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="">{t('settings.region.random')}</option>
                  {regionCodes.map((code) => {
                    const region = REGION_CODES[code];
                    const translatedProvince = getTranslatedRegionName(region.province);
                    const translatedCity = region.city ? getTranslatedRegionName(region.city) : '';
                    const translatedCounty = region.county ? getTranslatedRegionName(region.county) : '';

                    return (
                      <option key={code} value={code}>
                        {translatedProvince}{translatedCity ? ` - ${translatedCity}` : ''}{translatedCounty ? ` - ${translatedCounty}` : ''}
                      </option>
                    );
                  })}
                </select>
              </div>

              {/* 出生日期 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <Calendar className="w-4 h-4 inline mr-1" />
                  {t('settings.birthDate.label')}
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <select
                    value={birthYear}
                    onChange={(e) => setBirthYear(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {Array.from({ length: currentYear - 1900 + 1 }, (_, i) => currentYear - i).map((year) => (
                      <option key={year} value={year}>{year}{t('settings.birthDate.year')}</option>
                    ))}
                  </select>
                  <select
                    value={birthMonth}
                    onChange={(e) => setBirthMonth(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                      <option key={month} value={month}>{month}{t('settings.birthDate.month')}</option>
                    ))}
                  </select>
                  <select
                    value={birthDay}
                    onChange={(e) => setBirthDay(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {Array.from({ length: new Date(birthYear, birthMonth, 0).getDate() }, (_, i) => i + 1).map((day) => (
                      <option key={day} value={day}>{day}{t('settings.birthDate.day')}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* 性别选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <User className="w-4 h-4 inline mr-1" />
                  {t('settings.gender.label')}
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => setGender('male')}
                    className={`px-4 py-2 rounded-lg border transition-colors ${
                      gender === 'male'
                        ? 'bg-green-100 dark:bg-green-900/30 border-green-500 text-green-700 dark:text-green-300'
                        : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {t('settings.gender.male')}
                  </button>
                  <button
                    onClick={() => setGender('female')}
                    className={`px-4 py-2 rounded-lg border transition-colors ${
                      gender === 'female'
                        ? 'bg-green-100 dark:bg-green-900/30 border-green-500 text-green-700 dark:text-green-300'
                        : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {t('settings.gender.female')}
                  </button>
                  <button
                    onClick={() => setGender('random')}
                    className={`px-4 py-2 rounded-lg border transition-colors ${
                      gender === 'random'
                        ? 'bg-green-100 dark:bg-green-900/30 border-green-500 text-green-700 dark:text-green-300'
                        : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {t('settings.gender.random')}
                  </button>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="space-y-3 pt-4">
                <button
                  onClick={generateIdCard}
                  disabled={isGenerating}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                >
                  {isGenerating ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Shuffle className="w-4 h-4" />
                  )}
                  <span>{isGenerating ? t('actions.generating') : t('actions.generate')}</span>
                </button>
                
                <button
                  onClick={randomGenerate}
                  disabled={isGenerating}
                  className="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>{t('actions.randomGenerate')}</span>
                </button>
              </div>
            </div>
          </div>

          {/* 生成结果 */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">{t('result.title')}</h3>
            
            {generatedCard ? (
              <div className="space-y-6">
                {/* 身份证号码 */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-green-700 dark:text-green-300">{t('result.idNumber')}</span>
                    <button
                      onClick={copyIdCard}
                      className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-colors"
                      title={t('actions.copyIdCard')}
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="text-2xl font-mono font-bold text-green-900 dark:text-green-100 tracking-wider">
                    {generatedCard.idNumber}
                  </div>
                </div>

                {/* 姓名信息 */}
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                  <div className="text-sm text-blue-700 dark:text-blue-300 mb-1">{t('result.name')}</div>
                  <div className="text-xl font-medium text-blue-900 dark:text-blue-100">{generatedCard.name}</div>
                </div>

                {/* 详细信息 */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('result.region')}</div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {getTranslatedRegionName(generatedCard.province)}
                      {generatedCard.city && <><br />{getTranslatedRegionName(generatedCard.city)}</>}
                      {generatedCard.county && <><br />{getTranslatedRegionName(generatedCard.county)}</>}
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('result.birthDate')}</div>
                    <div className="font-medium text-gray-900 dark:text-white">{generatedCard.birthDate}</div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('result.age')}</div>
                    <div className="font-medium text-gray-900 dark:text-white">{generatedCard.age}{t('result.years')}</div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('result.gender')}</div>
                    <div className="font-medium text-gray-900 dark:text-white">{generatedCard.gender}</div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('result.constellation')}</div>
                    <div className="font-medium text-gray-900 dark:text-white">{generatedCard.constellation}</div>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('result.zodiac')}</div>
                    <div className="font-medium text-gray-900 dark:text-white">{generatedCard.zodiac}</div>
                  </div>
                </div>

                {/* 复制按钮 */}
                <button
                  onClick={copyFullInfo}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                >
                  <Copy className="w-4 h-4" />
                  <span>{t('actions.copyFullInfo')}</span>
                </button>

                {copySuccess && (
                  <div className="text-center text-sm text-green-600 dark:text-green-400">
                    ✓ {t('actions.copySuccess')}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shuffle className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-gray-500 dark:text-gray-400">{t('result.placeholder')}</p>
              </div>
            )}
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{t('instructions.title')}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600 dark:text-gray-300">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('instructions.features.title')}</h4>
              <ul className="space-y-1">
                <li>• {t('instructions.features.customizable')}</li>
                <li>• {t('instructions.features.checksum')}</li>
                <li>• {t('instructions.features.details')}</li>
                <li>• {t('instructions.features.copy')}</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">{t('instructions.notes.title')}</h4>
              <ul className="space-y-1">
                <li>• {t('instructions.notes.virtual')}</li>
                <li>• {t('instructions.notes.noReal')}</li>
                <li>• {t('instructions.notes.legal')}</li>
                <li>• {t('instructions.notes.forbidden')}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    

      {/* 微信公众号二维码 */}

      <div className="mt-12">

        <WechatQRSection size="medium" />

      </div>

      </div>
    </>
  );
}