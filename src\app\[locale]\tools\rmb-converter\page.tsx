'use client'

import { useState, useCallback, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'
import { defaultLocale } from '@/i18n'
import { Calculator, Copy, Info } from 'lucide-react'
import { SITE_NAME, SITE_URL } from '@/lib/constants'

import { WechatQRSection } from "@/app/_components/wechat-qr-section";
// 数字转中文大写映射
const DIGITS = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
const UNITS = ['', '拾', '佰', '仟']
const BIG_UNITS = ['', '万', '亿', '兆']

export default function RMBConverterPage() {
  const [amount, setAmount] = useState('')
  const [result, setResult] = useState('')
  const [error, setError] = useState('')

  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('pages.tools.tools.rmbConverter')

  // 如果是英文环境，显示说明而不是重定向
  const isEnglish = locale === 'en'

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // 这里可以添加提示
    } catch {
      console.error('复制失败')
    }
  }, [])

  // 转换数字为中文大写
  const convertToChineseUpper = useCallback((num: number): string => {
    if (num === 0) return '零元整'
    
    const numStr = num.toFixed(2)
    const [integerPart, decimalPart] = numStr.split('.')
    
    let result = ''
    
    // 处理整数部分
    if (parseInt(integerPart) === 0) {
      result = '零元'
    } else {
      result = convertIntegerPart(parseInt(integerPart)) + '元'
    }
    
    // 处理小数部分
    const jiao = parseInt(decimalPart[0])
    const fen = parseInt(decimalPart[1])
    
    if (jiao === 0 && fen === 0) {
      result += '整'
    } else {
      if (jiao > 0) {
        result += DIGITS[jiao] + '角'
      }
      if (fen > 0) {
        if (jiao === 0) {
          result += '零'
        }
        result += DIGITS[fen] + '分'
      }
    }
    
    return result
  }, [])

  // 转换整数部分
  const convertIntegerPart = (num: number): string => {
    if (num === 0) return ''
    
    const numStr = num.toString()
    const len = numStr.length
    let result = ''
    let zeroFlag = false
    
    for (let i = 0; i < len; i++) {
      const digit = parseInt(numStr[i])
      const pos = len - i - 1
      
      if (digit === 0) {
        zeroFlag = true
      } else {
        if (zeroFlag && result !== '') {
          result += '零'
        }
        result += DIGITS[digit]
        if (pos > 0) {
          result += getUnit(pos)
        }
        zeroFlag = false
      }
    }
    
    return result
  }

  // 获取单位
  const getUnit = (pos: number): string => {
    const bigUnitIndex = Math.floor(pos / 4)
    const unitIndex = pos % 4
    
    let unit = ''
    if (unitIndex > 0) {
      unit += UNITS[unitIndex]
    }
    if (bigUnitIndex > 0 && bigUnitIndex < BIG_UNITS.length) {
      unit += BIG_UNITS[bigUnitIndex]
    }
    
    return unit
  }

  // 处理转换
  const handleConvert = useCallback(() => {
    setError('')
    
    if (!amount.trim()) {
      setError('请输入金额')
      setResult('')
      return
    }
    
    const num = parseFloat(amount)
    
    if (isNaN(num)) {
      setError('请输入有效的数字')
      setResult('')
      return
    }
    
    if (num < 0) {
      setError('金额不能为负数')
      setResult('')
      return
    }
    
    if (num > 999999999999.99) {
      setError('金额过大，请输入小于1万亿的金额')
      setResult('')
      return
    }
    
    try {
      const chineseAmount = convertToChineseUpper(num)
      setResult(chineseAmount)
    } catch {
      setError('转换失败，请检查输入')
      setResult('')
    }
  }, [amount, convertToChineseUpper])

  // 常用示例
  const examples = [
    { amount: '123.45', description: '一百二十三元四角五分' },
    { amount: '1000', description: '一千元整' },
    { amount: '10000.50', description: '一万元五角' },
    { amount: '123456.78', description: '十二万三千四百五十六元七角八分' },
    { amount: '0.05', description: '零元零五分' }
  ]

  const handleExampleClick = (exampleAmount: string) => {
    setAmount(exampleAmount)
    const num = parseFloat(exampleAmount)
    const chineseAmount = convertToChineseUpper(num)
    setResult(chineseAmount)
    setError('')
  }

  // 如果是英文环境，显示说明页面
  if (isEnglish) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              💰 RMB to Chinese Uppercase Converter
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              This tool converts Arabic numerals to Chinese uppercase format for RMB (Chinese Yuan)
            </p>

            <div className="max-w-2xl mx-auto bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4">
                🇨🇳 Chinese-Specific Tool
              </h2>
              <p className="text-blue-700 dark:text-blue-300 mb-4">
                This tool is specifically designed for Chinese financial documents and requires Chinese language output.
                The converted results will always be in Chinese characters as required by Chinese financial regulations.
              </p>
              <p className="text-blue-700 dark:text-blue-300 mb-6">
                <strong>Example:</strong> 123.45 → 壹佰贰拾叁元肆角伍分
              </p>

              <div className="flex justify-center">
                <Link
                  href="/tools/rmb-converter"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Calculator className="w-5 h-5 mr-2" />
                  Use Tool (Chinese Interface)
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "人民币转大写工具",
            "description": "免费的在线人民币转大写工具，快速将阿拉伯数字金额转换为中文大写格式，适用于财务报表、发票开具等场景。",
            "url": `${SITE_URL}/tools/rmb-converter`,
            "applicationCategory": "FinanceApplication",
            "operatingSystem": "Any",
            "permissions": "browser",
            "isAccessibleForFree": true,
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "CNY"
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container-custom py-20">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href="/tools" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                  工具箱
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">人民币转大写</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            💰 人民币转数字大写工具
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            将阿拉伯数字金额转换为中文大写金额，适用于财务报表、支票填写等场景
          </p>
        </div>

        {/* 主要内容 */}
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-6">
            {/* 左侧：输入和结果区域 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 输入区域 */}
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    金额输入
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    输入需要转换的人民币金额（支持小数点后两位）
                  </p>
                </div>
                <div className="px-6 pb-6 space-y-4">
                  <div className="flex gap-2">
                    <input
                      type="number"
                      placeholder="例如：123.45"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleConvert()}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      step="0.01"
                      min="0"
                    />
                    <button
                      onClick={handleConvert}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    >
                      转换
                    </button>
                  </div>
                  
                  {error && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <span className="text-red-700 text-sm">{error}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* 转换结果 */}
              {result && (
                <div className="bg-white rounded-lg border shadow-sm">
                  <div className="p-6 pb-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Info className="h-5 w-5" />
                      转换结果
                    </h3>
                  </div>
                  <div className="px-6 pb-6">
                    <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm text-gray-600 mb-1">中文大写金额：</div>
                          <div className="text-lg font-medium text-green-800">{result}</div>
                        </div>
                        <button
                          onClick={() => copyToClipboard(result)}
                          className="p-2 text-green-600 hover:text-green-700 hover:bg-green-100 rounded"
                          title="复制结果"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 右侧：常用示例和说明 */}
            <div className="space-y-6">
              {/* 常用示例 */}
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold">常用示例</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    点击示例快速填入
                  </p>
                </div>
                <div className="px-6 pb-6">
                  <div className="space-y-2">
                    {examples.map((example, index) => (
                      <div
                        key={index}
                        className="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => handleExampleClick(example.amount)}
                      >
                        <div className="font-mono text-sm text-blue-600 mb-1">
                          ¥{example.amount}
                        </div>
                        <div className="text-xs text-gray-600">
                          {example.description}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 使用说明 */}
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold">使用说明</h3>
                </div>
                <div className="px-6 pb-6">
                  <div className="space-y-3 text-sm">
                    <div>
                      <div className="font-medium mb-1">支持范围：</div>
                      <div className="text-gray-600">0.01 - 999,999,999,999.99 元</div>
                    </div>
                    <div>
                      <div className="font-medium mb-1">精度：</div>
                      <div className="text-gray-600">支持到分（小数点后两位）</div>
                    </div>
                    <div>
                      <div className="font-medium mb-1">应用场景：</div>
                      <div className="text-gray-600 space-y-1">
                        <div>• 财务报表制作</div>
                        <div>• 支票金额填写</div>
                        <div>• 合同金额确认</div>
                        <div>• 发票开具</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}